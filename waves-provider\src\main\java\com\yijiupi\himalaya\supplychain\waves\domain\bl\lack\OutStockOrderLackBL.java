package com.yijiupi.himalaya.supplychain.waves.domain.bl.lack;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.distributedlock.annotation.BatchDistributeLock;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OrderExtendMapEnum;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuManageService;
import com.yijiupi.himalaya.supplychain.waves.advice.MarkMethodInvokeFlag;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.OrderCenterBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bl.orderconstraint.OrderConstraintCheckBL;
import com.yijiupi.himalaya.supplychain.waves.domain.bo.MarkLackBO;
import com.yijiupi.himalaya.supplychain.waves.domain.convertor.MarkLackConvertor;
import com.yijiupi.himalaya.supplychain.waves.domain.dao.OutStockOrderMapper;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderItemPO;
import com.yijiupi.himalaya.supplychain.waves.domain.po.OutStockOrderPO;
import com.yijiupi.himalaya.supplychain.waves.dto.batchtask.OrderItemTaskInfoDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.ordercenter.PartSendWsmDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockLackDTO;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.OutStockMarkLackDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchTaskConstants;
import com.yijiupi.supplychain.serviceutils.constant.redis.RedisConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/10/23
 */
@Service
public class OutStockOrderLackBL {

    @Autowired
    private OrderCenterBL orderCenterBL;
    @Autowired
    private OrderConstraintCheckBL orderConstraintCheckBL;
    @Autowired
    private OutStockOrderMapper outStockOrderMapper;
    @Reference
    private IProductSkuManageService productSkuManageService;

    private static final Logger logger = LoggerFactory.getLogger(OutStockOrderLackBL.class);

    @MarkMethodInvokeFlag(key = RedisConstant.SUP_F + "UserOnlineCache", warehouseId = "#dto.outStockLackDTOList[0].warehouseId", userId = "#dto.optUserId")
    @BatchDistributeLock(conditions = "#orderItemTaskInfoDTOS", property = "batchTaskId", expireMills = 60000, sleepMills = 3000,
            key = BatchTaskConstants.BATCH_TASK_COMPLETE_KEY, lockType = BatchDistributeLock.LockType.MUTEXLOCK)
    @Transactional(rollbackFor = Exception.class)
    public void markPartSend(OutStockMarkLackDTO dto, List<OrderItemTaskInfoDTO> orderItemTaskInfoDTOS) {
        markPartSend(dto);
    }

    /**
     * 调拨单和普通订单缺货调用中台接口不一样
     */
    @MarkMethodInvokeFlag(key = RedisConstant.SUP_F + "UserOnlineCache", warehouseId = "#dto.outStockLackDTOList[0].warehouseId", userId = "#dto.optUserId")
    @Transactional(rollbackFor = Exception.class)
    public void markPartSend(OutStockMarkLackDTO dto) {
        List<OutStockLackDTO> lackDTOList = dto.getOutStockLackDTOList();
        AssertUtils.notEmpty(lackDTOList, "参数不能为空");
        boolean allLack = lackDTOList.stream().anyMatch(m -> {
            Map<Long, Integer> itemMap = m.getItemMap();
            return itemMap.values().stream().allMatch(count -> count.compareTo(0) == 0);
        });
        if (BooleanUtils.isTrue(allLack)) {
            throw new BusinessValidateException("订单发货数量为0，无需发货，请联系小易到运营平台取消订单！");
        }
        List<PartSendWsmDTO> partSendWsmDTOList = lackDTOList.stream().map(p -> {
            PartSendWsmDTO partSendWsmDTO = new PartSendWsmDTO();
            BeanUtils.copyProperties(p, partSendWsmDTO);
            Map<Object, Object> extendMap = partSendWsmDTO.getExtendMap();
            if (Objects.isNull(extendMap)) {
                extendMap = new HashMap<>();
            }
            extendMap.put(OrderExtendMapEnum.标记缺货.getType(), p.getWarehouseId());
            partSendWsmDTO.setExtendMap(extendMap);

            return partSendWsmDTO;
        }).collect(Collectors.toList());
        Map<Long, PartSendWsmDTO> partSendWsmMap =
                partSendWsmDTOList.stream().collect(Collectors.toMap(PartSendWsmDTO::getBusinessId, v -> v));
        List<String> businessIds = lackDTOList.stream().map(OutStockLackDTO::getBusinessId).map(String::valueOf)
                .distinct().collect(Collectors.toList());
        List<Long> ids = outStockOrderMapper.findIdsByBusinessIds(businessIds, lackDTOList.get(0).getWarehouseId());
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessValidateException("出库单不存在！");
        }
        List<OutStockOrderPO> outStockOrderList = outStockOrderMapper.findByOrderIds(ids);
        if (CollectionUtils.isEmpty(outStockOrderList)) {
            throw new BusinessValidateException("出库单不存在！");
        }
        // SCM-12376 增加渠道和单据配置，支持按渠道设置是否支持缺货、部分配送、拆单发货等
        orderConstraintCheckBL.checkNPOrderLack(outStockOrderList);
        orderConstraintCheckBL.checkNormalOrderLack(outStockOrderList);
        Integer warehouseId = outStockOrderList.get(0).getWarehouseId();
        List<OutStockLackDTO> notInWarehouseList = dto.getOutStockLackDTOList().stream()
                .filter(lackDTO -> !warehouseId.equals(lackDTO.getWarehouseId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notInWarehouseList)) {
            Map<String, OutStockOrderPO> orderPOMap =
                    outStockOrderList.stream().collect(Collectors.toMap(OutStockOrderPO::getBusinessId, v -> v));
            List<String> orderNoList =
                    notInWarehouseList.stream().map(lackDTO -> orderPOMap.get(lackDTO.getBusinessId().toString()))
                            .filter(Objects::nonNull).map(OutStockOrderPO::getReforderno).collect(Collectors.toList());
            throw new BusinessValidateException("订单" + String.join(",", orderNoList) + "不属于当前仓库！");
        }

        // 校验产品能否缺货
        // OutStockLackDTO中
        // ItemMap<key=businessItemId,value=缺货最终数量>
        // itemShipMap<key=businessItemId,value=上次的数量>
        // businessItemId = outStockOrderList->OutStockOrderPO->OutStockOrderItemPO->businessItemId
        // skuId = outStockOrderList->OutStockOrderPO->OutStockOrderItemPO->skuid
        // productName = outStockOrderList->OutStockOrderPO->OutStockOrderItemPO->productname

        // 直接处理缺货相关的SKU和产品名称
        Map<Long, String> lackSkuToProductNameMap = lackDTOList.stream()
                .filter(lackDTO -> lackDTO.getItemMap() != null && lackDTO.getItemShipMap() != null)
                .flatMap(lackDTO -> {
                    // 为当前lackDTO构建businessItemId到OutStockOrderItemPO的映射
                    Map<String, OutStockOrderItemPO> itemMap = outStockOrderList.stream()
                            .flatMap(order -> order.getItems().stream())
                            .collect(Collectors.toMap(
                                    OutStockOrderItemPO::getBusinessItemId,
                                    item -> item,
                                    (existing, replacement) -> existing));

                    return lackDTO.getItemShipMap().entrySet().stream()
                            .filter(entry -> {
                                Integer finalCount = lackDTO.getItemMap().get(entry.getKey());
                                return finalCount != null && finalCount < entry.getValue();
                            })
                            .map(entry -> itemMap.get(entry.getKey().toString()))
                            .filter(itemPO -> itemPO != null && itemPO.getSkuid() != null);
                })
                .collect(Collectors.toMap(
                        OutStockOrderItemPO::getSkuid,
                        OutStockOrderItemPO::getProductname,
                        (existing, replacement) -> existing)); // 保留第一个值，避免重复

        if(CollectionUtils.isNotEmpty(lackSkuToProductNameMap)) {
            checkSkuCanBeOutOfStock(warehouseId, lackSkuToProductNameMap);
        }

        // 填充数据
        for (OutStockOrderPO outStockOrderPO : outStockOrderList) {
            Map<String, OutStockOrderItemPO> orderItemPOMap = outStockOrderPO.getItems().stream()
                    .collect(Collectors.toMap(OutStockOrderItemPO::getBusinessItemId, v -> v));
            PartSendWsmDTO partSendWsmDTO = partSendWsmMap.get(Long.valueOf(outStockOrderPO.getBusinessId()));
            StringBuffer lackInfo = MarkLackConvertor.getLackInfo(partSendWsmDTO, orderItemPOMap);
            MarkLackConvertor.fillShipMapInfo(outStockOrderPO, partSendWsmDTO);
            boolean isJiupiOrder = null != outStockOrderPO.getOrdertype()
                    && JiupiOrderTypeEnum.ORDER_TYPE_ALLOT == outStockOrderPO.getOrdertype();
            if (isJiupiOrder && !StringUtils.isEmpty(outStockOrderPO.getBusinessId())) {
                Map<String, StringBuffer> lackInfoMap = new HashMap<>();
                lackInfoMap.put(outStockOrderPO.getBusinessId(), lackInfo);
                orderCenterBL.orderLackNotify(MarkLackBO.ofWarehouseLack(lackInfoMap, dto.getOptUserId(), partSendWsmDTO));
            } else {
                Map<String, StringBuffer> lackInfoMap = new HashMap<>();
                lackInfoMap.put(outStockOrderPO.getBusinessId(), lackInfo);
                orderCenterBL.orderMarkNotify(MarkLackBO.ofWarehouseLack(lackInfoMap, dto.getOptUserId(), partSendWsmDTO));
            }
        }
    }

    /**
     * 校验产品是否允许缺货
     */
    private void checkSkuCanBeOutOfStock(Integer warehouseId, Map<Long, String> lackSkuToProductNameMap) {
        if (CollectionUtils.isEmpty(lackSkuToProductNameMap)) {
            return;
        }

        Map<Long, Boolean> canOutOfStockMap = productSkuManageService.checkSkuCanBeOutOfStock(warehouseId, new ArrayList<>(lackSkuToProductNameMap.keySet()));
        List<Long> restrictedSkuIds = canOutOfStockMap.entrySet().stream()
                .filter(entry -> Boolean.FALSE.equals(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        logger.info(String.format("校验缺货产品：%s，查询不能缺货结果：%s", JSON.toJSONString(lackSkuToProductNameMap), JSON.toJSONString(restrictedSkuIds)));

        if (!restrictedSkuIds.isEmpty()) {
            // 从lackSkuToProductNameMap中获取产品名称，拼接提示语
            Set<String> restrictedProductNames = restrictedSkuIds.stream()
                    .map(lackSkuToProductNameMap::get)
                    .filter(Objects::nonNull)
                    .filter(name -> !name.trim().isEmpty())
                    .collect(Collectors.toSet());

            String productNameStr = String.join("、", restrictedProductNames);
            String errorMessage = "以下产品未标记允许缺货，请联系仓管确认!";
            if (!productNameStr.isEmpty()) {
                errorMessage += "\n" + productNameStr;
            }

            throw new BusinessValidateException(errorMessage);
        }
    }

}
